import React, { use<PERSON><PERSON>back, useMemo, useState, useEffect } from "react";
import { useRegion } from "../../context/RegionContext";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableFooter,
  Paper,
  Box,
  CircularProgress,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Switch,
  FormControlLabel,
  Tooltip,
  Menu,
  MenuItem,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  FormGroup,
  Divider,
  Alert,
  Snackbar,
  Zoom,
  Fade,
} from "@mui/material";
import { ToastContainer, toast } from "react-toastify";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import StickyButtons from "./StickyButtons";
import MooeRow from "./MooeRow";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";
import "react-toastify/dist/ReactToastify.css";
import { NumericFormat } from "react-number-format";

// Custom component for numeric input formatting
const NumberFormatCustom = React.forwardRef(function NumberFormatCustom(props, ref) {
  const { onChange, ...other } = props;
  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      thousandSeparator
      decimalScale={2}
      fixedDecimalScale={false} // Don't force decimal places
      allowNegative={false} // Prevent negative values
      onValueChange={(values) => {
        // Only trigger onChange if the value actually changed
        // and is not empty or just formatting
        const numericValue = values.value || "0";
        onChange({
          target:
            {
              value: numericValue,
            },
        });
      }}
    />
  );
});
// Import icons
import SearchIcon from "@mui/icons-material/Search";
import FilterListIcon from "@mui/icons-material/FilterList";
import GetAppIcon from "@mui/icons-material/GetApp";
import SaveIcon from "@mui/icons-material/Save";
import ClearIcon from "@mui/icons-material/Clear";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import BarChartIcon from "@mui/icons-material/BarChart";
import TableChartIcon from "@mui/icons-material/TableChart";
import SummarizeIcon from "@mui/icons-material/Summarize";
import PrintIcon from "@mui/icons-material/Print";

const Mooe = ({ onDataChange }) => {
  const { currentUser } = useUser();
  const { activeRegion } = useRegion(); // Get the active region from context
  const queryClient = useQueryClient();
  const [expandedRows, setExpandedRows] = useState([]);
  const [disableIncomeInputs, setDisableIncomeInputs] = useState(false);

  // Enhanced state management
  const [searchTerm, setSearchTerm] = useState("");
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [selectedFilters, setSelectedFilters] = useState({
    sublineItems: [],
    amountRange: { min: "", max: "" },
    hasValues: false,
    emptyValues: false
  });
  const [autoSave, setAutoSave] = useState(false); // Default to false as requested
  const [bulkEditMode, setBulkEditMode] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [viewMode, setViewMode] = useState("table"); // table, chart, summary
  const [showMiniCharts, setShowMiniCharts] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [autoSaveTimer, setAutoSaveTimer] = useState(null);

  // Fetch data using React Query
  const { data, isLoading, error } = useQuery({
    queryKey: ["mooeData", activeRegion?.id], // Add region ID to query key to refetch when region changes
    queryFn: async () => {
      // Add region parameter to the API request
      const params = activeRegion?.id ? { region: activeRegion.id } : {};
      const response = await api.get("/mooe-data", { 
        params,
        withCredentials: true 
      });
      const { entries, status, settings } = response.data;
      const formatted = formatData(entries);

      return {
        formattedData: formatted,
        status: status || "Not Submitted",
        fiscalYear: settings?.fiscalYear || "",
        budgetType: settings?.budgetType || "",
      };
    },
    onError: (err) => {
      console.error("Error fetching MOOE data:", err);
      toast.error("Failed to load data. Please try again.");
    },
  });
  
  // Fetch IAs O&M Cost data
  const { data: iasOMCostData, isLoading: iasOMCostLoading } = useQuery({
    queryKey: ["iasOMCostData", activeRegion?.id], // Add region ID to query key
    queryFn: async () => {
      try {
        // Add region parameter to the API request
        const params = activeRegion?.id ? { region: activeRegion.id } : {};
        const response = await api.get("/ias-om-cost", { 
          params,
          withCredentials: true 
        });
        return response.data;
      } catch (err) {
        console.error("Error fetching IAs O&M Cost data:", err);
        // If the endpoint doesn't exist yet, return default values
        return { nis: 0, cis: 0 };
      }
    }
  });
  
  // State for IAs O&M Cost values
  const [iasOMCost, setIasOMCost] = useState({
    nis: "0",
    cis: "0",
    nisSubsidy: "0",
    cisSubsidy: "0"
  });

  // Save mutation
  const saveMutation = useMutation({
    mutationFn: (payload) => {
      // Include the region ID in the payload
      if (activeRegion?.id) {
        payload.region = activeRegion.id;
      }
      return api.post("/mooe-save", payload, { withCredentials: true });
    },
    onError: (err) => {
      console.error("Save failed:", err);
      toast.error("Failed to save data.");
    },
  });

  const formatData = useCallback((rawData) => {
    // Group by sublineItem but keep all accounting titles visible
    const grouped = {};
    for (let idx = 0; idx < rawData.length; idx++) {
      const item = rawData[idx];
      const key = item.sublineItem || `unknown-${idx}`;
      if (!grouped[key]) {
        grouped[key] = {
          id: key,
          sublineItem: item.sublineItem,
          children: [],
        };
      }
      grouped[key].children.push({
        ...item,
        id: `${key}-${item.uacsCode || "none"}-${idx}`,
      });
    }

    return Object.values(grouped);
  }, []);  // Calculate grand totals including IAs O&M Cost
  const grandTotals = useMemo(() => {
    if (!data?.formattedData) return {
      regularIncome: 0,
      regularSubsidy: 0,
      regularTotal: 0,
      income: 0,
      subsidy: 0,
      total: 0,
      iasIncomeTotal: 0,
      iasSubsidyTotal: 0,
      iasTotal: 0,
      ias: {
        nis: 0,
        cis: 0,
        nisSubsidy: 0,
        cisSubsidy: 0,
        total: 0
      }
    };

    // Calculate regular MOOE totals
    let regularIncome = 0;
    let regularSubsidy = 0;

    data.formattedData.forEach(row => {
      row.children.forEach(child => {
        // For regular MOOE, only include if it's not a special IAs O&M UACS code
        if (child.uacsCode !== "5-02-99-990-NIS" && child.uacsCode !== "5-02-99-990-CIS") {
          regularIncome += parseFloat(child.income || 0);
          regularSubsidy += parseFloat(child.subsidy || 0);
        }
      });
    });

    // Calculate IAs O&M Cost totals from dedicated fields
    const iasNIS = parseFloat(iasOMCost.nis || 0);
    const iasCIS = parseFloat(iasOMCost.cis || 0);
    const iasNISSubsidy = parseFloat(iasOMCost.nisSubsidy || 0);
    const iasCISSubsidy = parseFloat(iasOMCost.cisSubsidy || 0);

    const iasIncomeTotal = iasNIS + iasCIS;
    const iasSubsidyTotal = iasNISSubsidy + iasCISSubsidy;
    const iasTotal = iasIncomeTotal + iasSubsidyTotal;

    // Calculate combined totals
    const totalIncome = regularIncome + iasIncomeTotal;
    const totalSubsidy = regularSubsidy + iasSubsidyTotal;
    const regularTotal = regularIncome + regularSubsidy;
    const total = totalIncome + totalSubsidy;
    
    return { 
      regularIncome,
      regularSubsidy,
      regularTotal,
      income: totalIncome, 
      subsidy: totalSubsidy, 
      total,
      iasIncomeTotal,
      iasSubsidyTotal,
      iasTotal,
      ias: {
        nis: iasNIS,
        cis: iasCIS,
        nisSubsidy: iasNISSubsidy,
        cisSubsidy: iasCISSubsidy,
        total: iasTotal
      }
    };
  }, [data?.formattedData, iasOMCost]);

  // Keep all rows collapsed by default for better performance
  // Removed auto-expand functionality to improve loading speed

  // Calculate total function (moved up to avoid hoisting issues)
  const calculateTotal = useCallback((children) => {
    return children.reduce((sum, child) => {
      const income = parseFloat(child.income || 0);
      const subsidy = parseFloat(child.subsidy || 0);
      return sum + income + subsidy;
    }, 0);
  }, []);

  // Enhanced filtering logic
  const filteredData = useMemo(() => {
    if (!data?.formattedData) return [];

    return data.formattedData.filter(row => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSubline = row.sublineItem?.toLowerCase().includes(searchLower);
        const matchesChildren = row.children.some(child =>
          child.accountingTitle?.toLowerCase().includes(searchLower) ||
          child.uacsCode?.toLowerCase().includes(searchLower)
        );
        if (!matchesSubline && !matchesChildren) return false;
      }

      // Subline item filter
      if (selectedFilters.sublineItems.length > 0) {
        if (!selectedFilters.sublineItems.includes(row.sublineItem)) return false;
      }

      // Amount range filter
      if (selectedFilters.amountRange.min || selectedFilters.amountRange.max) {
        const totalAmount = calculateTotal(row.children);
        const min = parseFloat(selectedFilters.amountRange.min) || 0;
        const max = parseFloat(selectedFilters.amountRange.max) || Infinity;
        if (totalAmount < min || totalAmount > max) return false;
      }

      // Has values filter
      if (selectedFilters.hasValues) {
        const hasValues = row.children.some(child =>
          parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0
        );
        if (!hasValues) return false;
      }

      // Empty values filter
      if (selectedFilters.emptyValues) {
        const hasValues = row.children.some(child =>
          parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0
        );
        if (hasValues) return false;
      }

      return true;
    });
  }, [data?.formattedData, searchTerm, selectedFilters, calculateTotal]);

  // Track unsaved changes
  const trackChanges = useCallback(() => {
    setHasUnsavedChanges(true);
  }, []);

  // Define handleSave early, before any useCallback/useEffect that references it
  const handleSave = useCallback((isAutoSave = false) => {
    if (!data?.formattedData) return;

    // Check for validation errors
    if (Object.keys(validationErrors).length > 0) {
      if (!isAutoSave) {
        toast.error("Please fix validation errors before saving.");
      }
      console.warn("[MOOE] Validation errors present, aborting save", validationErrors);
      return;
    }

    // Get the current data from queryClient
    const currentData = queryClient.getQueryData(["mooeData", activeRegion?.id]);
    console.log("[MOOE] Current data from queryClient:", currentData);

    // If income inputs are disabled, preserve the original income values
    let entries;
    if (disableIncomeInputs && currentData?.formattedData) {
      // Create a map of original income values
      const originalIncomeMap = {};
      currentData.formattedData.forEach(row => {
        row.children.forEach(child => {
          const key = `${row.id}-${child.id}`;
          originalIncomeMap[key] = child.income;
        });
      });

      // Use original income values but updated subsidy values
      entries = data.formattedData
        .flatMap(row =>
          row.children.map(child => {
            const key = `${row.id}-${child.id}`;
            const originalIncome = originalIncomeMap[key] || child.income;
            const hasValues = parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0;
            const isNewEntry = hasValues && !child.status;
            return {
              ...child,
              sublineItem: row.sublineItem,
              status: isNewEntry ? "Not Submitted" : (child.status || data?.status),
              income: Number(disableIncomeInputs ? originalIncome : child.income) || 0,
              subsidy: Number(child.subsidy) || 0,
              amount: Number(disableIncomeInputs ? originalIncome : child.income) + Number(child.subsidy) || 0
            };
          })
        )
        .filter(entry => {
          const hasNonZeroValues = Number(entry.income) !== 0 || Number(entry.subsidy) !== 0;
          return hasNonZeroValues;
        });
    } else {
      // Normal processing if income inputs are not disabled
      entries = data.formattedData
        .flatMap(row =>
          row.children.map(child => {
            // Always treat empty string as zero for income/subsidy
            const incomeVal = Number(child.income === undefined || child.income === null || child.income === "" ? 0 : child.income);
            const subsidyVal = Number(child.subsidy === undefined || child.subsidy === null || child.subsidy === "" ? 0 : child.subsidy);
            const hasValues = incomeVal > 0 || subsidyVal > 0;
            const isNewEntry = hasValues && !child.status;
            return {
              ...child,
              sublineItem: row.sublineItem,
              status: isNewEntry ? "Not Submitted" : (child.status || data?.status),
              income: incomeVal,
              subsidy: subsidyVal,
              amount: incomeVal + subsidyVal
            };
          })
        )
        // Remove this filter to allow saving even if all are zero
        // .filter(entry => {
        //   const hasNonZeroValues = Number(entry.income) !== 0 || Number(entry.subsidy) !== 0;
        //   return hasNonZeroValues;
        // });
    }

    // Add IAs O&M Cost entries if they have values
    const iasOMCostEntries = [];
    if (parseFloat(iasOMCost.nis || 0) > 0 || parseFloat(iasOMCost.nisSubsidy || 0) > 0) {
      iasOMCostEntries.push({
        sublineItem: "Irrigators' Associations (IAs) Operation & Maintenance Cost",
        accountingTitle: "National Irrigation System",
        uacsCode: "5-02-99-990-NIS",
        income: Number(iasOMCost.nis) || 0,
        subsidy: Number(iasOMCost.nisSubsidy) || 0,
        amount: Number(iasOMCost.nis) + Number(iasOMCost.nisSubsidy) || 0,
        status: data?.status || "Not Submitted"
      });
    }
    if (parseFloat(iasOMCost.cis || 0) > 0 || parseFloat(iasOMCost.cisSubsidy || 0) > 0) {
      iasOMCostEntries.push({
        sublineItem: "Irrigators' Associations (IAs) Operation & Maintenance Cost",
        accountingTitle: "Communal Irrigation System",
        uacsCode: "5-02-99-990-CIS",
        income: Number(iasOMCost.cis) || 0,
        subsidy: Number(iasOMCost.cisSubsidy) || 0,
        amount: Number(iasOMCost.cis) + Number(iasOMCost.cisSubsidy) || 0,
        status: data?.status || "Not Submitted"
      });
    }

    const allEntries = [...entries, ...iasOMCostEntries];

    // Defensive: Prevent save if no entries at all (not just non-zero)
    if ((!entries || entries.length === 0) && (!iasOMCostEntries || iasOMCostEntries.length === 0)) {
      toast.error("No entries to save. Please enter some income or subsidy amounts.");
      return;
    }

    // Ensure all required fields are present and correct type
    const requiredFields = ["sublineItem", "accountingTitle", "uacsCode", "income", "subsidy", "amount", "status"];
    for (const entry of allEntries) {
      for (const field of requiredFields) {
        if (entry[field] === undefined || entry[field] === null || (typeof entry[field] === "string" && entry[field].trim() === "")) {
          toast.error(`Missing required field: ${field} in one or more entries.`);
          return;
        }
      }
      // Type check for numbers
      if (typeof entry.income !== "number" || typeof entry.subsidy !== "number" || typeof entry.amount !== "number") {
        toast.error("Income, Subsidy, and Amount must be numbers.");
        return;
      }
    }

    // Allow saving even if there are no entries with values
    // This will save the current state (including zero values for existing entries)
    console.log("[MOOE] Preparing to save entries:", allEntries.length, "entries");

    const payload = {
      meta: {
        processBy: `${currentUser.FirstName} ${currentUser.LastName}`,
        processDate: new Date(),
        region: activeRegion?.id || currentUser.Region, // Use the active region from context
        fiscalYear: data?.fiscalYear,
        budgetType: data?.budgetType,
        status: data?.status,
      },
      entries: allEntries,
    };

    console.log("[MOOE] Saving payload:", JSON.stringify(payload, null, 2));

    // Save all MOOE data including IAs O&M Cost entries
    saveMutation.mutate(payload, {
      onSuccess: (response) => {
        console.log("[MOOE] Save response:", response.data);
        setHasUnsavedChanges(false);
        setLastSaved(new Date());

        if (isAutoSave) {
          toast.success("Auto-saved successfully!", { autoClose: 2000 });
        } else {
          toast.success("Data saved successfully!");
        }
        // Force UI refresh after save
        setTimeout(() => {
          queryClient.invalidateQueries(["mooeData"]);
          queryClient.invalidateQueries(["consolidatedSummary"]);
          window.location.reload();
        }, 500);
      },
      onError: (error) => {
        console.error("[MOOE] Save error:", error);
        if (error.response?.status === 400) {
          const errorMessage = error.response?.data?.error || "Bad request";
          if (errorMessage.includes("No valid entries")) {
            toast.error("No valid entries to save. Please enter some income or subsidy amounts.");
          } else {
            toast.error(`Save failed: ${errorMessage}`);
          }
        } else if (error.response?.status === 404) {
          toast.error("Save endpoint not found. Please check your connection.");
        } else {
          toast.error("Failed to save data. Please try again.");
        }
      }
    });
  }, [data, validationErrors, queryClient, activeRegion, disableIncomeInputs, iasOMCost, currentUser, saveMutation]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && hasUnsavedChanges) {
      if (autoSaveTimer) clearTimeout(autoSaveTimer);
      const timer = setTimeout(() => {
        handleSave(true); // true indicates auto-save
      }, 3000); // Auto-save after 3 seconds of inactivity
      setAutoSaveTimer(timer);
      return () => clearTimeout(timer); // <-- Fix: cleanup timer
    }
    return () => { if (autoSaveTimer) clearTimeout(autoSaveTimer); } // <-- Fix: always cleanup
  }, [autoSave, hasUnsavedChanges, handleSave]);
  
  // Update IAs O&M Cost state when data is loaded
  useEffect(() => {
    if (iasOMCostData) {
      setIasOMCost({
        nis: iasOMCostData.nis.toString(),
        cis: iasOMCostData.cis.toString(),
        nisSubsidy: iasOMCostData.nisSubsidy ? iasOMCostData.nisSubsidy.toString() : "0",
        cisSubsidy: iasOMCostData.cisSubsidy ? iasOMCostData.cisSubsidy.toString() : "0"
      });
    }
  }, [iasOMCostData]);
  
  // Refetch data when region changes
  useEffect(() => {
    if (activeRegion) {
      // Invalidate queries to trigger refetch with new region
      queryClient.invalidateQueries(["mooeData"]);
      queryClient.invalidateQueries(["iasOMCostData"]);
    }
  }, [activeRegion, queryClient]);

  const toggleExpandRow = useCallback((rowId) => {
    setExpandedRows(prev =>
      prev.includes(rowId)
        ? prev.filter(id => id !== rowId)
        : [...prev, rowId]
    );
  }, []);

  const handleAmountChange = useCallback((rowId, childId, value) => {
    // Validate input
    const numValue = parseFloat(value) || 0;
    if (numValue < 0) {
      setValidationErrors(prev => ({
        ...prev,
        [`${rowId}-${childId}-amount`]: "Amount cannot be negative"
      }));
      return;
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${rowId}-${childId}-amount`];
        return newErrors;
      });
    }

    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;

      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;

          return {
            ...row,
            children: row.children.map(child =>
              child.id === childId ? { ...child, amount: value } : child
            )
          };
        })
      };
    });

    trackChanges();
  }, [queryClient, trackChanges]);

  const handleIncomeChange = useCallback((rowId, childId, value) => {
    if (disableIncomeInputs) {
      toast.warning("Income fields are locked because the total matches the Corporate Income projection. To make changes, adjust the Corporate Income first.");
      return;
    }
    const stringValue = value === "" ? "" : value;
    const numValue = parseFloat(stringValue) || 0;
    if (stringValue !== "" && (isNaN(numValue) || numValue < 0)) {
      setValidationErrors(prev => ({
        ...prev,
        [`${rowId}-${childId}-income`]: "Income must be a valid positive number"
      }));
      return;
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${rowId}-${childId}-income`];
        return newErrors;
      });
    }
    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;
      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;
          return {
            ...row,
            children: row.children.map(child => {
              if (child.id !== childId) return child;
              // Always use latest values for autocompute
              // Use numbers for computation, but store as string for display
              const newIncome = stringValue === "" ? "0" : stringValue;
              // If user is typing, use their input for income, but always compute amount as number
              const subsidyVal = parseFloat(child.subsidy === "" ? 0 : child.subsidy || 0);
              const incomeVal = parseFloat(newIncome === "" ? 0 : newIncome);
              const amountVal = incomeVal + subsidyVal;
              return {
                ...child,
                income: newIncome,
                amount: amountVal.toString(),
              };
            })
          };
        })
      };
    });
    trackChanges();
    if (autoSave) {
      if (autoSaveTimer) clearTimeout(autoSaveTimer);
      setAutoSaveTimer(setTimeout(() => {
        handleSave(true);
      }, 3000));
    }
  }, [queryClient, disableIncomeInputs, trackChanges, autoSave, autoSaveTimer, handleSave]);

  const handleSubsidyChange = useCallback((rowId, childId, value) => {
    const stringValue = value === "" ? "" : value;
    const numValue = parseFloat(stringValue) || 0;
    if (stringValue !== "" && (isNaN(numValue) || numValue < 0)) {
      setValidationErrors(prev => ({
        ...prev,
        [`${rowId}-${childId}-subsidy`]: "Subsidy must be a valid positive number"
      }));
      return;
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${rowId}-${childId}-subsidy`];
        return newErrors;
      });
    }
    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;
      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;
          return {
            ...row,
            children: row.children.map(child => {
              if (child.id !== childId) return child;
              // Always use latest values for autocompute
              const newSubsidy = stringValue === "" ? "0" : stringValue;
              const incomeVal = parseFloat(child.income === "" ? 0 : child.income || 0);
              const subsidyVal = parseFloat(newSubsidy === "" ? 0 : newSubsidy);
              const amountVal = incomeVal + subsidyVal;
              return {
                ...child,
                subsidy: newSubsidy,
                amount: amountVal.toString(),
              };
            })
          };
        })
      };
    });
    trackChanges();
    if (autoSave) {
      if (autoSaveTimer) clearTimeout(autoSaveTimer);
      setAutoSaveTimer(setTimeout(() => {
        handleSave(true);
      }, 3000));
    }
  }, [queryClient, trackChanges, autoSave, autoSaveTimer, handleSave]);

  const handleTitleChange = useCallback((rowId, childId, value) => {
    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;
      
      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;
          
          return {
            ...row,
            children: row.children.map(child => 
              child.id === childId ? { ...child, accountingTitle: value } : child
            )
          };
        })
      };
    });
  }, [queryClient]);
  
  // Handlers for IAs O&M Cost input fields
  const handleIAsOMCostChange = (field) => (event) => {
    let value = event.target.value;
    // Don't convert empty string to 0 - preserve the actual input
    const stringValue = value === "" ? "" : value;
    const numValue = parseFloat(stringValue);
    // Only validate if there's actually a value
    if (stringValue !== "" && (isNaN(numValue) || numValue < 0)) {
      setValidationErrors(prev => ({
        ...prev,
        [`ias-${field}`]: `${field.toUpperCase()} must be a valid positive number`
      }));
      return;
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`ias-${field}`]; // <-- Fix: actually remove error
        return newErrors;
      });
    }
    setIasOMCost(prev => ({ ...prev, [field]: stringValue === "" ? "0" : stringValue }));
    setHasUnsavedChanges(true);
  };

  const addCustomMOOE = useCallback((rowId) => {
    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;
      
      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;
          
          // Find the Other MOOE category to get its details
          const otherMooeItem = row.children.find(child => child.uacsCode === "5-02-99-990");
          
          if (!otherMooeItem) return row;
          
          return {
            ...row,
            children: [
              ...row.children,
              {
                id: `${rowId}-custom-${Date.now()}`,
                accountingTitle: "Custom MOOE",
                uacsCode: "5-02-99-990",
                amount: "0",
                income: "0",
                subsidy: "0",
                custom: true,
                sublineItem: row.sublineItem
              }
            ]
          };
        })
      };
    });
  }, [queryClient]);

  const handleClear = () => {
    queryClient.invalidateQueries(["mooeData"]);
  };

  return (
    <div>
      <ToastContainer />
      <Paper elevation={3} style={{ padding: 20, borderRadius: 10 }}>
        <Typography variant="h4" gutterBottom>
          MOOE (Maintenance and Other Operating Expenses)
        </Typography>
        <Divider style={{ marginBottom: 20 }} />
        {/* Loading and error states handled here */}
        {isLoading ? (
          <Box display="flex" justifyContent="center" alignItems="center" height="300px">
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box>Error loading data</Box>
        ) : (
          <>
            {/* Toolbar with enhanced features */}
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Box display="flex" alignItems="center">
                <Typography variant="subtitle1" style={{ marginRight: 10 }}>
                  View as:
                </Typography>
                <Button
                  variant={viewMode === "table" ? "contained" : "outlined"}
                  color="primary"
                  size="small"
                  onClick={() => setViewMode("table")}
                  startIcon={<TableChartIcon />}
                  style={{ marginRight: 5 }}
                >
                  Table
                </Button>
                <Button
                  variant={viewMode === "chart" ? "contained" : "outlined"}
                  color="primary"
                  size="small"
                  onClick={() => setViewMode("chart")}
                  startIcon={<BarChartIcon />}
                  style={{ marginRight: 5 }}
                >
                  Chart
                </Button>
                <Button
                  variant={viewMode === "summary" ? "contained" : "outlined"}
                  color="primary"
                  size="small"
                  onClick={() => setViewMode("summary")}
                  startIcon={<SummarizeIcon />}
                >
                  Summary
                </Button>
              </Box>
              <Box>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={() => setExportDialogOpen(true)}
                  startIcon={<GetAppIcon />}
                  style={{ marginRight: 10 }}
                >
                  Export
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handlePrint}
                  startIcon={<PrintIcon />}
                >
                  Print
                </Button>
              </Box>
            </Box>

            {/* Search and filter section */}
            <Box display="flex" alignItems="center" mb={2}>
              <TextField
                label="Search"
                variant="outlined"
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{ marginRight: 10, flex: 1 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={() => setFilterAnchorEl(true)}
                startIcon={<FilterListIcon />}
              >
                Filter
              </Button>
            </Box>

            {/* Filter menu - enhanced with more options */}
            <Menu
              anchorEl={filterAnchorEl}
              open={Boolean(filterAnchorEl)}
              onClose={() => setFilterAnchorEl(null)}
              TransitionComponent={Zoom}
            >
              <MenuItem disabled>
                <Typography variant="subtitle1">Filter Options</Typography>
              </MenuItem>
              <Divider />
              <MenuItem>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedFilters.hasValues}
                      onChange={(e) => setSelectedFilters({ ...selectedFilters, hasValues: e.target.checked })}
                      color="primary"
                    />
                  }
                  label="Has Values"
                />
              </MenuItem>
              <MenuItem>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedFilters.emptyValues}
                      onChange={(e) => setSelectedFilters({ ...selectedFilters, emptyValues: e.target.checked })}
                      color="primary"
                    />
                  }
                  label="Empty Values"
                />
              </MenuItem>
              <Divider />
              <Box p={2}>
                <Typography variant="subtitle2" gutterBottom>
                  Amount Range
                </Typography>
                <Box display="flex" alignItems="center">
                  <TextField
                    label="Min"
                    variant="outlined"
                    size="small"
                    type="number"
                    value={selectedFilters.amountRange.min}
                    onChange={(e) => setSelectedFilters({ ...selectedFilters, amountRange: { ...selectedFilters.amountRange, min: e.target.value } })}
                    style={{ marginRight: 10, flex: 1 }}
                  />
                  <TextField
                    label="Max"
                    variant="outlined"
                    size="small"
                    type="number"
                    value={selectedFilters.amountRange.max}
                    onChange={(e) => setSelectedFilters({ ...selectedFilters, amountRange: { ...selectedFilters.amountRange, max: e.target.value } })}
                    style={{ flex: 1 }}
                  />
                </Box>
              </Box>
              <MenuItem onClick={() => setFilterAnchorEl(null)}>
                <Button
                  variant="contained"
                  color="primary"
                  size="small"
                  fullWidth
                >
                  Apply Filters
                </Button>
              </MenuItem>
            </Menu>

            {/* Display mode specific content */}
            {viewMode === "table" && (
              <>
                {/* Table for detailed view */}
                <TableContainer component={Paper} style={{ borderRadius: 10, overflow: "hidden" }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell rowSpan={2}>Subline Item</TableCell>
                        <TableCell rowSpan={2}>Accounting Title</TableCell>
                        <TableCell rowSpan={2}>UACS Code</TableCell>
                        <TableCell colSpan={2}>Income</TableCell>
                        <TableCell colSpan={2}>Subsidy</TableCell>
                        <TableCell rowSpan={2}>Total</TableCell>
                        <TableCell rowSpan={2}>Action</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Regular</TableCell>
                        <TableCell>IAs O&M Cost</TableCell>
                        <TableCell>Regular</TableCell>
                        <TableCell>IAs O&M Cost</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredData.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={9} style={{ textAlign: "center", padding: 20 }}>
                            No data available for the selected filters.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredData.map((row) => (
                          <React.Fragment key={row.id}>
                            {/* Parent row for subline item */}
                            <TableRow>
                              <TableCell rowSpan={row.children.length + 1}>
                                {row.sublineItem}
                              </TableCell>
                              <TableCell colSpan={8} style={{ backgroundColor: "#f9f9f9" }}>
                                {/* Custom row for total per subline item */}
                                <Box display="flex" justifyContent="flex-end" p={1}>
                                  <Typography variant="subtitle1" style={{ marginRight: 10 }}>
                                    Subtotal:
                                  </Typography>
                                  <Typography variant="subtitle1" color="primary">
                                    {calculateTotal(row.children).toFixed(2)}
                                  </Typography>
                                </Box>
                              </TableCell>
                            </TableRow>
                            {/* Child rows for each accounting title under the subline item */}
                            {row.children.map((child) => (
                              <TableRow key={child.id}>
                                <TableCell>{child.accountingTitle}</TableCell>
                                <TableCell>{child.uacsCode}</TableCell>
                                <TableCell>
                                  <NumberFormatCustom
                                    value={child.income}
                                    onChange={(e) => handleIncomeChange(row.id, child.id, e.target.value)}
                                    disabled={!isEditable || disableIncomeInputs}
                                    error={Boolean(validationErrors[`${row.id}-${child.id}-income`])}
                                    helperText={validationErrors[`${row.id}-${child.id}-income`]}
                                  />
                                </TableCell>
                                <TableCell>
                                  <NumberFormatCustom
                                    value={child.subsidy}
                                    onChange={(e) => handleSubsidyChange(row.id, child.id, e.target.value)}
                                    disabled={!isEditable}
                                    error={Boolean(validationErrors[`${row.id}-${child.id}-subsidy`])}
                                    helperText={validationErrors[`${row.id}-${child.id}-subsidy`]}
                                  />
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body1" color="textSecondary">
                                    {child.amount}
                                  </Typography>
                                </TableCell>
                                <TableCell>
                                  <IconButton
                                    color="primary"
                                    onClick={() => toggleExpandRow(row.id)}
                                    size="small"
                                  >
                                    {expandedRows.includes(row.id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                                  </IconButton>
                                </TableCell>
                              </TableRow>
                            ))}
                          </React.Fragment>
                        ))
                      )}
                    </TableBody>
                    <TableFooter>
                      <TableRow>
                        <TableCell colSpan={3} style={{ textAlign: "right", fontWeight: "bold" }}>
                          Grand Total:
                        </TableCell>
                        <TableCell>
                          <NumberFormatCustom
                            value={grandTotals.income}
                            displayType="text"
                            thousandSeparator
                            decimalScale={2}
                            fixedDecimalScale
                          />
                        </TableCell>
                        <TableCell>
                          <NumberFormatCustom
                            value={grandTotals.subsidy}
                            displayType="text"
                            thousandSeparator
                            decimalScale={2}
                            fixedDecimalScale
                          />
                        </TableCell>
                        <TableCell>
                          <NumberFormatCustom
                            value={grandTotals.total}
                            displayType="text"
                            thousandSeparator
                            decimalScale={2}
                            fixedDecimalScale
                          />
                        </TableCell>
                        <TableCell colSpan={2} />
                      </TableRow>
                    </TableFooter>
                  </Table>
                </TableContainer>
              </>
            )}

            {/* Chart view - to be implemented */}
            {viewMode === "chart" && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Chart View (Coming Soon)
                </Typography>
                <Divider style={{ marginBottom: 20 }} />
                <Box display="flex" justifyContent="center" alignItems="center" height="400px">
                  <Typography variant="body1" color="textSecondary">
                    Chart view is not yet implemented. Please switch to table view for now.
                  </Typography>
                </Box>
              </Box>
            )}

            {/* Summary view - to be implemented */}
            {viewMode === "summary" && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Summary View (Coming Soon)
                </Typography>
                <Divider style={{ marginBottom: 20 }} />
                <Box display="flex" justifyContent="center" alignItems="center" height="400px">
                  <Typography variant="body1" color="textSecondary">
                    Summary view is not yet implemented. Please switch to table view for now.
                  </Typography>
                </Box>
              </Box>
            )}
          </>
        )}
        {/* Sticky buttons and dialogs should always be rendered, but can be disabled if loading/error */}
        <StickyButtons
          onSave={handleSave}
          onAutoSaveToggle={() => setAutoSave(!autoSave)}
          autoSaveEnabled={autoSave}
          saveDisabled={isSaveDisabled || isLoading || !!error}
          onClear={handleClear}
          onExport={() => setExportDialogOpen(true)}
          onPrint={handlePrint}
          isLoading={saveMutation.isLoading}
        />
      </Paper>
      {/* Export dialog - always rendered */}
      <Dialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Export MOOE Data</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Choose the format for exporting the data:
          </Typography>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={true}
                  color="primary"
                  disabled
                />
              }
              label="Excel (.xlsx)"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={false}
                  color="primary"
                  disabled
                />
              }
              label="CSV (.csv) - Coming Soon"
            />
          </FormGroup>
          <Divider style={{ margin: "10px 0" }} />
          <Typography variant="body2" color="textSecondary">
            Note: CSV export is not yet available. Please use Excel format for now.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setExportDialogOpen(false)}
            color="primary"
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              handleExportToExcel();
              setExportDialogOpen(false);
            }}
            color="secondary"
            variant="contained"
          >
            Export
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default Mooe;
